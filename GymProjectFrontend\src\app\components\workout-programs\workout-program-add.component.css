/* Workout Program Add Wizard Styles */

/* Progress Bar Enhancements */
.progress {
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: var(--border-radius-pill);
}

.progress-bar {
  transition: width 0.6s ease;
  border-radius: var(--border-radius-pill);
}

/* Step Navigation Buttons */
.btn {
  transition: all var(--transition-speed) var(--transition-timing);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Day Count Selection Cards */
.form-check {
  margin-bottom: 0;
}

.form-check-input[type="radio"] {
  margin-top: 0;
}

.form-check-label .modern-card {
  transition: all var(--transition-speed) var(--transition-timing);
  border: 2px solid transparent;
}

.form-check-input:checked + .form-check-label .modern-card {
  border-color: var(--primary);
  background-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.form-check-label:hover .modern-card {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Day Cards */
.day-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  background-color: var(--bg-primary);
  transition: all var(--transition-speed) var(--transition-timing);
}

.day-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-2px);
}

.day-header {
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.day-body {
  padding: 1.25rem;
}

.day-number {
  font-weight: 600;
  color: var(--primary);
}

.form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

.form-check-input:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--primary-light);
}

.form-check-label {
  font-weight: 500;
  margin-left: 0.5rem;
}

.is-invalid {
  border-color: var(--danger);
}

.invalid-feedback {
  display: block;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875rem;
  color: var(--danger);
}

.alert {
  padding: 0.75rem 1rem;
  margin-bottom: 0;
  border: 1px solid transparent;
  border-radius: var(--border-radius-md);
}

.alert-info {
  color: var(--info);
  background-color: var(--info-light);
  border-color: rgba(var(--info-rgb), 0.2);
}

.alert-warning {
  color: var(--warning);
  background-color: var(--warning-light);
  border-color: rgba(var(--warning-rgb), 0.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .day-header {
    padding: 0.75rem 1rem;
  }
  
  .day-body {
    padding: 1rem;
  }
  
  .modern-btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
  
  .d-flex.gap-2 {
    flex-direction: column;
    gap: 0.5rem !important;
  }
}

/* Dark mode specific adjustments */
[data-theme="dark"] .day-card {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .day-header {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .form-check-input {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
}

[data-theme="dark"] .form-check-input:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}

[data-theme="dark"] .alert-info {
  background-color: var(--info-light);
  border-color: rgba(var(--info-rgb), 0.3);
  color: var(--info);
}

[data-theme="dark"] .alert-warning {
  background-color: var(--warning-light);
  border-color: rgba(var(--warning-rgb), 0.3);
  color: var(--warning);
}

/* Wizard Step Animations */
.fade-in {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Exercise Setup Cards */
.exercise-setup-card {
  transition: all var(--transition-speed) var(--transition-timing);
  cursor: pointer;
}

.exercise-setup-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.exercise-setup-card.configured {
  border-color: var(--success);
  background-color: var(--success-light);
}

.exercise-setup-card.not-configured {
  border-color: var(--warning);
  background-color: var(--warning-light);
}

/* Preview Section */
.preview-summary {
  background: linear-gradient(135deg, var(--primary-light), var(--info-light));
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

/* Step Navigation Improvements */
.step-nav-btn {
  min-height: 80px;
  text-align: left;
  border: 2px solid transparent;
  transition: all var(--transition-speed) var(--transition-timing);
}

.step-nav-btn.active {
  border-color: var(--primary);
  background-color: var(--primary);
  color: white;
}

.step-nav-btn.completed {
  border-color: var(--success);
  background-color: var(--success-light);
  color: var(--success);
}

.step-nav-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.step-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Dark Mode Enhancements */
[data-theme="dark"] .form-check-input:checked + .form-check-label .modern-card {
  background-color: var(--primary-light);
  border-color: var(--primary);
}

[data-theme="dark"] .preview-summary {
  background: linear-gradient(135deg, rgba(var(--primary-rgb), 0.2), rgba(var(--info-rgb), 0.2));
}

[data-theme="dark"] .step-nav-btn.completed {
  background-color: var(--success-light);
  color: var(--success);
}

/* Responsive Improvements */
@media (max-width: 768px) {
  .step-nav-btn {
    min-height: 60px;
    font-size: 0.875rem;
  }

  .step-nav-btn .fw-bold {
    font-size: 0.8rem;
  }

  .step-nav-btn small {
    font-size: 0.7rem;
  }

  .modern-card-body {
    padding: var(--spacing-md);
  }

  .row.g-3 {
    --bs-gutter-x: 1rem;
    --bs-gutter-y: 1rem;
  }
}
